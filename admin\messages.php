<?php
session_start();
include '../config.php';

if (!isLoggedIn()) {
    redirectToLogin();
}

$message = '';

// إنشاء جدول الرسائل إذا لم يكن موجوداً
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS contact_messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        subject VARCHAR(255),
        message TEXT NOT NULL,
        status ENUM('new', 'read') DEFAULT 'new',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // إضافة رسائل تجريبية إذا كان الجدول فارغ
    $stmt = $pdo->query("SELECT COUNT(*) FROM contact_messages");
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        $pdo->exec("INSERT INTO contact_messages (name, email, phone, subject, message, status) VALUES
            ('أحمد محمد', '<EMAIL>', '01234567890', 'استفسار عن الخدمات', 'أريد معرفة المزيد عن خدماتكم في التسويق الإلكتروني', 'new'),
            ('فاطمة علي', '<EMAIL>', '01987654321', 'طلب عرض سعر', 'أحتاج عرض سعر لتصميم موقع إلكتروني لشركتي', 'new'),
            ('محمد حسن', '<EMAIL>', '01555666777', 'شكر وتقدير', 'شكراً لكم على الخدمة الممتازة التي قدمتموها لنا', 'read')");
    }
} catch(PDOException $e) {
    // في حالة وجود خطأ في إنشاء الجدول
}

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // التحقق من CSRF Token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'رمز الأمان غير صحيح';
    } elseif (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'mark_read':
                $id = (int)$_POST['id'];
                try {
                    $stmt = $pdo->prepare("UPDATE contact_messages SET status = 'read' WHERE id = ?");
                    $stmt->execute([$id]);
                    $message = 'تم تحديث حالة الرسالة';
                } catch(PDOException $e) {
                    $message = 'حدث خطأ أثناء التحديث';
                }
                break;
                
            case 'delete':
                $id = (int)$_POST['id'];
                try {
                    $stmt = $pdo->prepare("DELETE FROM contact_messages WHERE id = ?");
                    $stmt->execute([$id]);
                    $message = 'تم حذف الرسالة بنجاح';
                } catch(PDOException $e) {
                    $message = 'حدث خطأ أثناء حذف الرسالة';
                }
                break;
        }
    }
}

// جلب الرسائل
try {
    $stmt = $pdo->query("SELECT * FROM contact_messages ORDER BY created_at DESC");
    $messages = $stmt->fetchAll();
} catch(PDOException $e) {
    $messages = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الرسائل - لوحة التحكم</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/logo2.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/logo2.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <?php include 'includes/admin-styles.php'; ?>

    <style>






        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .page-header h2 {
            margin: 0;
            color: var(--text-color);
            font-weight: 800;
            font-size: 1.8rem;
        }

        .card {
            border: none;
            border-radius: 20px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 1.5rem;
            border: none;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 700;
        }

        .card-body {
            padding: 2rem;
        }

        .message-new {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
        }

        .btn {
            border-radius: 50px;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        .table {
            border-radius: 15px;
            overflow: hidden;
        }

        .table th {
            background: var(--light-bg);
            border: none;
            font-weight: 700;
            color: var(--text-color);
        }

        .table td {
            border: none;
            vertical-align: middle;
        }
        }
        
        .message-read {
            background: #f8f9fa;
        }
        
        .btn {
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <div style="display: flex; align-items: center;">
                <img src="../assets/images/logo2.png" alt="Logo" style="width: 40px; height: 40px; margin-left: 15px;">
                <h2>إدارة الرسائل</h2>
            </div>
        </div>
                
                <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
                
                <!-- قائمة الرسائل -->
                <div class="card">
                    <div class="card-header">
                        <h5>الرسائل الواردة</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($messages): ?>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>الرسالة</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($messages as $msg): ?>
                                    <tr class="<?php echo $msg['status'] == 'new' ? 'message-new' : 'message-read'; ?>">
                                        <td><?php echo htmlspecialchars($msg['name']); ?></td>
                                        <td>
                                            <a href="mailto:<?php echo htmlspecialchars($msg['email']); ?>">
                                                <?php echo htmlspecialchars($msg['email']); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php if ($msg['phone']): ?>
                                            <a href="tel:<?php echo htmlspecialchars($msg['phone']); ?>">
                                                <?php echo htmlspecialchars($msg['phone']); ?>
                                            </a>
                                            <?php else: ?>
                                            -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewMessage(<?php echo $msg['id']; ?>)">
                                                عرض الرسالة
                                            </button>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($msg['created_at'])); ?></td>
                                        <td>
                                            <?php if ($msg['status'] == 'new'): ?>
                                            <span class="badge bg-warning">جديدة</span>
                                            <?php else: ?>
                                            <span class="badge bg-success">مقروءة</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($msg['status'] == 'new'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_read">
                                                <input type="hidden" name="id" value="<?php echo $msg['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-success">
                                                    <i class="fas fa-check"></i> تحديد كمقروءة
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                            
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $msg['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </form>
                                        </td>
                                    </tr>


                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد رسائل حالياً</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

    <!-- Modal واحد لعرض الرسائل -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-envelope me-2"></i>
                        <span id="modalTitle">عرض الرسالة</span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- سيتم ملء المحتوى بالجافاسكريبت -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إغلاق
                    </button>
                    <a href="#" id="replyBtn" class="btn btn-primary">
                        <i class="fas fa-reply me-2"></i>رد بالبريد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    function viewMessage(id) {
        console.log('Viewing message ID:', id);

        // إظهار loading
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = '<div class="text-center p-4"><i class="fas fa-spinner fa-spin fa-2x text-primary"></i><br><small class="text-muted mt-2">جاري التحميل...</small></div>';

        // فتح الـ modal أولاً
        const modal = new bootstrap.Modal(document.getElementById('messageModal'));
        modal.show();

        // جلب بيانات الرسالة
        fetch('get_message.php?id=' + id)
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.text().then(text => {
                    console.log('Raw response:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                    }
                });
            })
            .then(data => {
                console.log('Parsed data:', data);

                if (data.success && data.message) {
                    const msg = data.message;

                    // تحديث عنوان الـ modal
                    document.getElementById('modalTitle').textContent = 'رسالة من ' + (msg.name || 'غير محدد');

                    // تحديث محتوى الـ modal
                    modalBody.innerHTML = `
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="info-item p-3 bg-light rounded">
                                    <strong class="text-primary"><i class="fas fa-user me-2"></i>الاسم:</strong>
                                    <p class="mb-0 mt-1">${msg.name || 'غير محدد'}</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="info-item p-3 bg-light rounded">
                                    <strong class="text-primary"><i class="fas fa-envelope me-2"></i>البريد الإلكتروني:</strong>
                                    <p class="mb-0 mt-1">${msg.email || 'غير محدد'}</p>
                                </div>
                            </div>
                            ${msg.phone ? `
                            <div class="col-md-6 mb-3">
                                <div class="info-item p-3 bg-light rounded">
                                    <strong class="text-primary"><i class="fas fa-phone me-2"></i>الهاتف:</strong>
                                    <p class="mb-0 mt-1">${msg.phone}</p>
                                </div>
                            </div>
                            ` : ''}
                            ${msg.subject ? `
                            <div class="col-md-6 mb-3">
                                <div class="info-item p-3 bg-light rounded">
                                    <strong class="text-primary"><i class="fas fa-tag me-2"></i>الموضوع:</strong>
                                    <p class="mb-0 mt-1">${msg.subject}</p>
                                </div>
                            </div>
                            ` : ''}
                            <div class="col-md-6 mb-3">
                                <div class="info-item p-3 bg-light rounded">
                                    <strong class="text-primary"><i class="fas fa-calendar me-2"></i>التاريخ:</strong>
                                    <p class="mb-0 mt-1">${msg.created_at ? new Date(msg.created_at).toLocaleString('ar-EG') : 'غير محدد'}</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <strong class="text-primary d-block mb-2">
                                <i class="fas fa-comment-dots me-2"></i>نص الرسالة:
                            </strong>
                            <div class="p-3 bg-light rounded border-start border-primary border-4">
                                ${msg.message ? msg.message.replace(/\n/g, '<br>') : 'لا يوجد محتوى'}
                            </div>
                        </div>
                    `;

                    // تحديث رابط الرد
                    const replyBtn = document.getElementById('replyBtn');
                    if (msg.email) {
                        replyBtn.href = 'mailto:' + msg.email;
                        replyBtn.style.display = 'inline-block';
                    } else {
                        replyBtn.style.display = 'none';
                    }
                } else {
                    console.error('Error in response:', data);
                    modalBody.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            خطأ في جلب بيانات الرسالة: ${data.message || 'خطأ غير معروف'}
                            ${data.debug ? '<br><small>Debug: ' + data.debug + '</small>' : ''}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                modalBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في الاتصال: ${error.message}
                    </div>
                `;
            });
    }
    </script>
</body>
</html>
