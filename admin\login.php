<?php
session_start();
define('SECURE_ACCESS', true);
include '../config.php';
include '../includes/recaptcha.php';

if (isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$error = '';

// جلب إعدادات reCAPTCHA
$recaptcha_settings = getRecaptchaSettings($pdo);

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = cleanInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = 'جميع الحقول مطلوبة';
    } elseif (!validateRecaptchaInForm($pdo)) {
        $error = 'يرجى التحقق من أنك لست روبوت';
    } else {
        try {
            $stmt = $pdo->prepare("SELECT * FROM admin WHERE username = ?");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();

            if ($admin && password_verify($password, $admin['password'])) {
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                header('Location: index.php');
                exit;
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch(PDOException $e) {
            $error = 'حدث خطأ أثناء تسجيل الدخول';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/logo2.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/logo2.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <?php echo getRecaptchaScript($recaptcha_settings['enabled']); ?>

    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --success-color: #10b981;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --white-color: #ffffff;
            --text-color: #374151;
            --border-color: #e5e7eb;
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
            padding: 20px 10px;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.5;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
            color: white;
        }

        .floating-element:nth-child(1) {
            top: 10%;
            left: 10%;
            font-size: 2rem;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 20%;
            right: 15%;
            font-size: 1.5rem;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 20%;
            left: 20%;
            font-size: 1.8rem;
            animation-delay: 4s;
        }

        .floating-element:nth-child(4) {
            bottom: 30%;
            right: 10%;
            font-size: 2.2rem;
            animation-delay: 1s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        .login-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 400px;
            padding: 10px;
            margin: auto;
        }
        
        .login-card {
            background: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .login-header {
            background: var(--gradient-primary);
            color: white;
            padding: 30px 25px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .login-header .icon-container {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            position: relative;
            z-index: 2;
        }

        .login-header .icon-container::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
        }
        
        .login-header i {
            font-size: 2rem;
            position: relative;
            z-index: 3;
        }

        .login-header h3 {
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }

        .login-header p {
            opacity: 0.9;
            margin-bottom: 0;
            position: relative;
            z-index: 2;
        }
        
        .login-body {
            padding: 35px 30px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        .form-floating {
            margin-bottom: 25px;
            position: relative;
        }

        .form-floating .form-control {
            padding-top: 25px;
            padding-bottom: 15px;
        }

        .form-floating .form-control:focus ~ label,
        .form-floating .form-control:not(:placeholder-shown) ~ label {
            opacity: 0.8;
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
            color: var(--primary-color);
        }
        
        .form-control {
            border-radius: var(--border-radius);
            border: 2px solid var(--border-color);
            padding: 18px 20px;
            font-size: 16px;
            font-weight: 500;
            transition: var(--transition);
            background: var(--light-color);
            height: auto;
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.1);
            background: var(--white-color);
        }

        .form-floating > label {
            color: var(--text-color);
            font-weight: 600;
            font-family: 'Tajawal', sans-serif;
            font-size: 15px;
            right: 20px;
            left: auto;
        }

        .form-control::placeholder {
            color: #9ca3af;
            font-weight: 400;
            font-family: 'Tajawal', sans-serif;
        }
        
        .btn-login {
            background: var(--gradient-primary);
            border: none;
            border-radius: var(--border-radius);
            padding: 18px 20px;
            font-weight: 700;
            width: 100%;
            color: white;
            font-size: 1.1rem;
            font-family: 'Tajawal', sans-serif;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: var(--transition);
        }

        .btn-login:hover::before {
            left: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            color: white;
        }
        
        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 15px 20px;
            margin-bottom: 25px;
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        .back-link {
            text-align: center;
            margin-top: 30px;
        }

        .back-link a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
            font-weight: 500;
        }

        .back-link a:hover {
            color: var(--accent-color);
        }

        /* تحسينات reCAPTCHA */
        .g-recaptcha {
            display: flex;
            justify-content: center;
            margin: 1.5rem 0;
        }

        .g-recaptcha > div {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }



        /* تحسينات للشاشات الصغيرة */
        @media (max-height: 700px) {
            body {
                align-items: flex-start;
                padding-top: 10px;
            }
        }

        @media (max-width: 576px) {
            .login-container {
                padding: 5px;
                max-width: 350px;
            }

            .login-header {
                padding: 25px 20px;
            }

            .login-body {
                padding: 25px 20px;
            }

            .floating-element {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Elements -->
    <div class="floating-element"><i class="fas fa-rocket"></i></div>
    <div class="floating-element"><i class="fas fa-chart-line"></i></div>
    <div class="floating-element"><i class="fas fa-bullhorn"></i></div>
    <div class="floating-element"><i class="fas fa-globe"></i></div>

    <div class="login-container" data-aos="zoom-in" data-aos-duration="800">
        <div class="login-card">
            <div class="login-header">
                <div class="icon-container">
                    <img src="../assets/images/logo2.png" alt="شركة نقرة للتسويق الإلكتروني" style="width: 60px; height: 60px; border-radius: 50%;">
                </div>
                <h3>لوحة التحكم</h3>
                <p>شركة نقرة للتسويق الإلكتروني</p>
            </div>
            
            <div class="login-body">
                <?php if ($error): ?>
                <div class="alert alert-danger" data-aos="shake" data-aos-duration="500">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error; ?>
                </div>
                <?php endif; ?>
                
                <form method="POST" data-aos="fade-up" data-aos-duration="600" data-aos-delay="200">
                    <div class="form-floating position-relative">
                        <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                        <label for="username"><i class="fas fa-user me-2"></i>اسم المستخدم</label>
                    </div>

                    <div class="form-floating position-relative">
                        <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                        <label for="password"><i class="fas fa-lock me-2"></i>كلمة المرور</label>
                    </div>

                    <?php if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])): ?>
                    <div class="mb-3">
                        <div class="g-recaptcha" data-sitekey="<?php echo htmlspecialchars($recaptcha_settings['site_key']); ?>"></div>
                    </div>
                    <?php endif; ?>

                    <button type="submit" class="btn btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </form>
            </div>
        </div>
        
        <div class="back-link" data-aos="fade-up" data-aos-duration="600" data-aos-delay="400">
            <a href="../index.php">
                <i class="fas fa-arrow-left me-1"></i>
                العودة للموقع الرئيسي
            </a>
        </div>
    </div>



    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Add focus effects
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-3px)';
                this.parentElement.style.boxShadow = '0 8px 25px rgba(37, 99, 235, 0.15)';
                this.style.borderColor = 'var(--primary-color)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
                this.parentElement.style.boxShadow = 'none';
                if (!this.value) {
                    this.style.borderColor = 'var(--border-color)';
                }
            });

            // تأثير الكتابة
            input.addEventListener('input', function() {
                if (this.value.length > 0) {
                    this.style.borderColor = 'var(--success-color)';
                    this.style.background = '#f0fdf4';
                } else {
                    this.style.borderColor = 'var(--border-color)';
                    this.style.background = 'var(--light-color)';
                }
            });
        });

        // Enhanced button hover effect
        document.querySelector('.btn-login').addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });
        
        document.querySelector('.btn-login').addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });


    </script>
</body>
</html>
