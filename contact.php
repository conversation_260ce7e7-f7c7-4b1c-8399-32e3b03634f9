<?php
session_start();
define('SECURE_ACCESS', true);
include 'config.php';
include 'includes/recaptcha.php';

$success_message = '';
$error_message = '';

// جلب إعدادات reCAPTCHA
$recaptcha_settings = getRecaptchaSettings($pdo);

if ($_POST) {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');

    if (empty($name) || empty($email) || empty($message)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'يرجى إدخال بريد إلكتروني صحيح';
    } elseif (!validateRecaptchaInForm($pdo)) {
        $error_message = 'يرجى التحقق من أنك لست روبوت';
    } else {
        try {
            $stmt = $pdo->prepare("INSERT INTO contact_messages (name, email, phone, subject, message, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$name, $email, $phone, $subject, $message]);
            $success_message = 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.';

            // مسح البيانات بعد الإرسال الناجح
            $name = $email = $phone = $subject = $message = '';
        } catch(PDOException $e) {
            $error_message = 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تواصل معنا - شركة نقرة للتسويق الإلكتروني</title>
    <meta name="description" content="تواصل مع شركة نقرة للتسويق الإلكتروني للحصول على استشارة مجانية وخدمات تسويقية متميزة">
    <meta name="keywords" content="تواصل, استشارة مجانية, تسويق إلكتروني, شركة نقرة">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/logo2.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/logo2.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <?php echo getRecaptchaScript($recaptcha_settings['enabled']); ?>

    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #2563eb;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --text-color: #1f2937;
            --light-bg: #f8fafc;
            --white: #ffffff;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-accent: linear-gradient(135deg, var(--accent-color), #d97706);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--white);
        }

        /* Header */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.2rem;
            color: var(--primary-color) !important;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .navbar-brand img {
            height: 45px;
            margin-left: 15px;
        }

        .navbar-brand .company-name {
            display: flex;
            flex-direction: column;
            line-height: 1.2;
        }

        .navbar-brand .company-name .main-name {
            font-size: 1.1rem;
            font-weight: 800;
            color: var(--primary-color);
        }

        .navbar-brand .company-name .sub-name {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--accent-color);
        }

        .nav-link {
            font-weight: 500;
            color: var(--text-color) !important;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--primary-color) !important;
        }

        /* Page Header */
        .page-header {
            background: var(--gradient-primary);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .page-header-content {
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1rem;
        }

        .page-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Contact Section */
        .contact-section {
            padding: 80px 0;
            background: var(--light-bg);
        }

        .contact-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }

        .contact-info {
            background: var(--gradient-primary);
            color: white;
            border-radius: 20px;
            padding: 3rem;
            height: 100%;
        }

        .contact-info h3 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 1rem;
            font-size: 1.2rem;
        }

        .contact-details h5 {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .contact-details p {
            margin: 0;
            opacity: 0.9;
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        .btn-submit {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }

        .alert {
            border-radius: 10px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .back-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }

        .back-btn:hover {
            background: var(--secondary-color);
            color: white;
            transform: translateY(-2px);
        }

        .back-btn i {
            margin-left: 10px;
        }

        /* Footer */
        .footer {
            background: var(--text-color);
            color: white;
            padding: 60px 0 20px;
            margin-top: 80px;
        }

        .footer h5 {
            color: white;
            font-weight: 700;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .footer h5::after {
            content: '';
            position: absolute;
            bottom: -8px;
            right: 0;
            width: 50px;
            height: 3px;
            background: var(--accent-color);
            border-radius: 2px;
        }

        .footer a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: var(--accent-color);
        }

        .footer .social-links {
            display: flex;
            gap: 10px;
        }

        .footer .social-links a {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .footer .social-links a:hover {
            background: var(--accent-color);
            transform: translateY(-3px);
            color: white;
        }

        /* reCAPTCHA Styling */
        .g-recaptcha {
            display: flex;
            justify-content: center;
            margin: 1.5rem 0;
        }

        .g-recaptcha > div {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .navbar-brand .company-name .main-name {
                font-size: 0.9rem !important;
                font-weight: 700;
            }

            .navbar-brand .company-name .sub-name {
                font-size: 0.7rem !important;
                font-weight: 400;
            }

            .navbar-brand img {
                height: 35px;
                margin-left: 10px;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .page-header p {
                font-size: 1rem;
            }

            .contact-card, .contact-info {
                padding: 2rem;
            }

            .navbar-brand .company-name {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="assets/images/logo2.png" alt="شركة نقرة للتسويق الإلكتروني">
                <div class="company-name">
                    <span class="main-name">شركة نقرة للتسويق الإلكتروني</span>
                    <span class="sub-name">Nakra Digital Marketing</span>
                </div>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.php">أعمالنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.php">تواصل معنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#pricing">الخطط</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="articles.php">المقالات</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content" data-aos="fade-up">
                <h1>تواصل معنا</h1>
                <p>نحن هنا لمساعدتك في تحقيق أهدافك الرقمية. تواصل معنا الآن واحصل على استشارة مجانية</p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <a href="index.php" class="back-btn" data-aos="fade-right">
                <i class="fas fa-arrow-right"></i>
                العودة للرئيسية
            </a>

            <div class="row">
                <div class="col-lg-8 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="contact-card">
                        <h2 class="mb-4">أرسل لنا رسالة</h2>

                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success_message; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">الموضوع</label>
                                    <input type="text" class="form-control" id="subject" name="subject" value="<?php echo htmlspecialchars($subject ?? ''); ?>">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="message" class="form-label">الرسالة *</label>
                                <textarea class="form-control" id="message" name="message" rows="5" required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                            </div>

                            <?php if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])): ?>
                            <div class="mb-3">
                                <div class="g-recaptcha" data-sitekey="<?php echo htmlspecialchars($recaptcha_settings['site_key']); ?>"></div>
                            </div>
                            <?php endif; ?>

                            <button type="submit" class="btn-submit">
                                <i class="fas fa-paper-plane me-2"></i>
                                إرسال الرسالة
                            </button>
                        </form>
                    </div>
                </div>

                <div class="col-lg-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="contact-info">
                        <h3>معلومات التواصل</h3>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-details">
                                <h5>الهاتف</h5>
                                <p><?php echo COMPANY_PHONE; ?></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div class="contact-details">
                                <h5>واتساب</h5>
                                <p>تواصل معنا عبر الواتساب</p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h5>البريد الإلكتروني</h5>
                                <p><?php echo COMPANY_EMAIL; ?></p>
                            </div>
                        </div>

                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-details">
                                <h5>العنوان</h5>
                                <p><?php echo COMPANY_ADDRESS; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <!-- معلومات الشركة -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>شركة نقرة للتسويق الإلكتروني</h5>
                    <p>شركة متخصصة في التسويق الإلكتروني والاستضافة والإعلانات الممولة منذ 2018. نساعد الشركات على النمو في العالم الرقمي.</p>
                    <div class="social-links mt-3">
                        <a href="#" title="فيسبوك"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" title="تويتر"><i class="fab fa-twitter"></i></a>
                        <a href="#" title="إنستجرام"><i class="fab fa-instagram"></i></a>
                        <a href="#" title="لينكد إن"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php"><i class="fas fa-chevron-left me-2"></i>الرئيسية</a></li>
                        <li><a href="services.php"><i class="fas fa-chevron-left me-2"></i>خدماتنا</a></li>
                        <li><a href="projects.php"><i class="fas fa-chevron-left me-2"></i>أعمالنا</a></li>
                        <li><a href="index.php#pricing"><i class="fas fa-chevron-left me-2"></i>الخطط</a></li>
                        <li><a href="contact.php"><i class="fas fa-chevron-left me-2"></i>تواصل معنا</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>المقالات</a></li>
                    </ul>
                </div>

                <!-- أحدث المقالات -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>أحدث المقالات</h5>
                    <ul class="list-unstyled">
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>تحسين محركات البحث</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>إعلانات جوجل الفعالة</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>تصميم المواقع الحديثة</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>التسويق الرقمي</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>عرض جميع المقالات</a></li>
                    </ul>
                </div>

                <!-- معلومات التواصل -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>معلومات التواصل</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-phone me-2 text-warning"></i>
                            <a href="tel:<?php echo COMPANY_PHONE; ?>"><?php echo COMPANY_PHONE; ?></a>
                        </li>
                        <li class="mb-2">
                            <i class="fab fa-whatsapp me-2 text-success"></i>
                            <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>">واتساب</a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2 text-info"></i>
                            <a href="mailto:<?php echo COMPANY_EMAIL; ?>"><?php echo COMPANY_EMAIL; ?></a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt me-2 text-danger"></i>
                            <?php echo COMPANY_ADDRESS; ?>
                        </li>
                    </ul>
                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 شركة نقرة للتسويق الإلكتروني. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">تم التطوير بواسطة <strong>نقرة للتسويق الإلكتروني</strong></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 800,
            once: true
        });
    </script>
</body>
</html>
