<?php
session_start();
include '../config.php';

if (!isLoggedIn()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

header('Content-Type: application/json; charset=utf-8');

$type = sanitizePath($_GET['type'] ?? 'all'); // all, projects, articles
$allowedTypes = ['all', 'projects', 'articles'];
if (!in_array($type, $allowedTypes)) {
    $type = 'all';
}

$images = [];
$basePath = '../assets/images/';

try {
    // جلب الصور من المجلد الرئيسي
    if ($type === 'all' || $type === 'projects') {
        $mainImages = glob($basePath . '*.{jpg,jpeg,png,gif,webp,PNG,JPG,JPEG}', GLOB_BRACE);
        foreach ($mainImages as $image) {
            $relativePath = str_replace('../', '', $image);
            $filename = basename($image);
            
            // تجاهل الشعارات
            if (!in_array($filename, ['logo.png', 'logo2.png'])) {
                $images[] = [
                    'path' => $relativePath,
                    'name' => $filename,
                    'size' => filesize($image),
                    'type' => 'main'
                ];
            }
        }
    }
    
    // جلب الصور من مجلد المشاريع
    if ($type === 'all' || $type === 'projects') {
        $projectImages = glob($basePath . 'projects/*.{jpg,jpeg,png,gif,webp,PNG,JPG,JPEG}', GLOB_BRACE);
        foreach ($projectImages as $image) {
            $relativePath = str_replace('../', '', $image);
            $filename = basename($image);
            
            $images[] = [
                'path' => $relativePath,
                'name' => $filename,
                'size' => filesize($image),
                'type' => 'projects'
            ];
        }
    }
    
    // جلب الصور من مجلد المقالات
    if ($type === 'all' || $type === 'articles') {
        $articleImages = glob($basePath . 'articles/*.{jpg,jpeg,png,gif,webp,PNG,JPG,JPEG}', GLOB_BRACE);
        foreach ($articleImages as $image) {
            $relativePath = str_replace('../', '', $image);
            $filename = basename($image);
            
            $images[] = [
                'path' => $relativePath,
                'name' => $filename,
                'size' => filesize($image),
                'type' => 'articles'
            ];
        }
    }
    
    // ترتيب الصور حسب التاريخ (الأحدث أولاً)
    usort($images, function($a, $b) {
        return filemtime('../' . $b['path']) - filemtime('../' . $a['path']);
    });
    
    echo json_encode([
        'success' => true,
        'images' => $images,
        'total' => count($images)
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في جلب الصور: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
