<?php
// منع الوصول المباشر
if (!defined('SECURE_ACCESS')) {
    die('Access Denied');
}

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'nakrywqo_nakraformarketing');
define('DB_USER', 'nakrywqo_nakraformarketing');
define('DB_PASS', 'ASDasd123123123225434571');

// إعدادات الموقع
define('SITE_URL', 'https://www.nakraformarketing.com');
define('SITE_NAME', 'شركة نقرة للتسويق الإلكتروني');
define('COMPANY_PHONE', '01062751630');
define('COMPANY_EMAIL', '<EMAIL>');
define('COMPANY_ADDRESS', 'مصر - المنصورة - قرية نشا');
define('WHATSAPP_NUMBER', '2001062751630');

// الاتصال بقاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", 
        DB_USER, 
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات");
}

// وظائف مساعدة
function cleanInput($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function sanitizeForDatabase($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function validateInput($data, $type = 'text') {
    $data = trim($data);

    switch($type) {
        case 'email':
            return filter_var($data, FILTER_VALIDATE_EMAIL);
        case 'url':
            return filter_var($data, FILTER_VALIDATE_URL);
        case 'int':
            return filter_var($data, FILTER_VALIDATE_INT);
        case 'text':
        default:
            return htmlspecialchars(strip_tags($data), ENT_QUOTES, 'UTF-8');
    }
}

function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

function redirectToLogin() {
    header('Location: admin/login.php');
    exit;
}

// حماية CSRF
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// حماية من Path Traversal
function sanitizePath($path) {
    // إزالة المسارات الخطيرة
    $path = str_replace(['../', '..\\', '../', '..\\'], '', $path);
    $path = preg_replace('/[^a-zA-Z0-9\-_\.\/]/', '', $path);
    return $path;
}

// حماية من SQL Injection إضافية
function validateId($id) {
    return filter_var($id, FILTER_VALIDATE_INT, array("options" => array("min_range" => 1)));
}
?>
