# حماية مجلد الإدارة
# منع الوصول للملفات الحساسة
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

<Files "*.old">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

# حماية من الهجمات الشائعة
RewriteEngine On

# منع SQL Injection
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE) [NC,OR]
RewriteCond %{QUERY_STRING} (\.\./|\.\.\%2F|\.\.\%5C) [NC,OR]
RewriteCond %{QUERY_STRING} (localhost|loopback|127\.0\.0\.1) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*(\>|%3E) [NC]
RewriteRule ^(.*)$ - [F,L]

# حماية من Path Traversal
RewriteCond %{THE_REQUEST} \s/+(.*/)?\.\.(/.*)?[\s?] [NC]
RewriteRule ^ - [F,L]

# منع الوصول للملفات المخفية
RewriteCond %{REQUEST_FILENAME} -f
RewriteCond %{REQUEST_FILENAME} "/\."
RewriteRule ^ - [F,L]

# حماية إضافية للأمان
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google.com https://www.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';"
</IfModule>

# تحديد حد الذاكرة وحجم الملفات
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_vars 3000

# منع عرض محتويات المجلد
Options -Indexes

# منع الوصول المباشر لملفات PHP معينة
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>
