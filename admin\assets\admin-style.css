/* Admin Panel Unified Styles */
:root {
    --primary-color: #3b82f6;
    --secondary-color: #2563eb;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --text-color: #1f2937;
    --light-bg: #f8fafc;
    --white: #ffffff;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), #d97706);
    --gradient-success: linear-gradient(135deg, var(--success-color), #059669);
    --gradient-danger: linear-gradient(135deg, var(--danger-color), #dc2626);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    color: var(--text-color);
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    background: var(--gradient-primary);
    min-height: 100vh;
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    padding: 0;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.sidebar-brand img {
    width: 50px;
    height: 50px;
    margin-left: 15px;
}

.sidebar-brand h4 {
    color: var(--primary-color);
    font-weight: 800;
    font-size: 1.2rem;
    margin: 0;
}

.admin-info {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-weight: 500;
}

.sidebar nav {
    padding: 1rem 0;
}

.sidebar a {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 4px solid transparent;
    position: relative;
}

.sidebar a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.sidebar a:hover::before,
.sidebar a.active::before {
    transform: scaleX(1);
}

.sidebar a:hover,
.sidebar a.active {
    color: white;
    border-right-color: var(--accent-color);
}

.sidebar a i {
    margin-left: 15px;
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}

.sidebar a span {
    position: relative;
    z-index: 2;
    font-weight: 500;
}

/* Main Content */
.main-content {
    margin-right: 280px;
    padding: 2rem;
    min-height: 100vh;
}

.page-header {
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.page-header h2 {
    margin: 0;
    color: var(--text-color);
    font-weight: 800;
    font-size: 1.8rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 20px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 20px 20px 0 0 !important;
    padding: 1.5rem;
    border: none;
}

.card-header h5 {
    margin: 0;
    font-weight: 700;
}

.card-body {
    padding: 2rem;
}

/* Buttons */
.btn {
    border-radius: 50px;
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Alerts */
.alert {
    border-radius: 15px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e5e7eb;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Tables */
.table {
    border-radius: 15px;
    overflow: hidden;
}

.table th {
    background: var(--light-bg);
    border: none;
    font-weight: 700;
    color: var(--text-color);
}

.table td {
    border: none;
    vertical-align: middle;
}

/* Modals */
.modal-content {
    border-radius: 20px;
    border: none;
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: 20px 20px 0 0;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
        width: 100%;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
        padding: 1rem;
    }

    .page-header {
        padding: 1rem;
    }

    .page-header h2 {
        font-size: 1.5rem;
    }
}
