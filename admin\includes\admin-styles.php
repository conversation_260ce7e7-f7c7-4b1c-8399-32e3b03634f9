<style>
    :root {
        --primary-color: #3b82f6;
        --secondary-color: #2563eb;
        --accent-color: #f59e0b;
        --success-color: #10b981;
        --danger-color: #ef4444;
        --text-color: #1f2937;
        --light-bg: #f8fafc;
        --white: #ffffff;
        --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        --gradient-accent: linear-gradient(135deg, var(--accent-color), #d97706);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Tajawal', sans-serif;
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        color: var(--text-color);
        line-height: 1.6;
        overflow-x: hidden;
    }

    /* Sidebar Styles */
    .sidebar {
        position: fixed;
        top: 0;
        right: 0;
        width: 280px;
        height: 100vh;
        background: var(--gradient-primary);
        z-index: 1000;
        overflow-y: auto;
        overflow-x: hidden;
        box-shadow: var(--shadow-lg);
        backdrop-filter: blur(20px);
        border-left: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        flex-direction: column;
    }

    .sidebar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg,
            rgba(59, 130, 246, 0.1) 0%,
            rgba(37, 99, 235, 0.1) 50%,
            rgba(245, 158, 11, 0.1) 100%);
        pointer-events: none;
    }

    .sidebar-header {
        padding: 2rem 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        text-align: center;
        position: relative;
        z-index: 2;
        flex-shrink: 0;
    }

    .sidebar-header .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        background: rgba(255, 255, 255, 0.95);
        padding: 1rem;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
    }

    .sidebar-header .logo img {
        width: 45px;
        height: 45px;
        margin-left: 15px;
        border-radius: 10px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    }

    .sidebar-header .logo span {
        color: var(--primary-color);
        font-size: 1.2rem;
        font-weight: 800;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .admin-info {
        color: rgba(255, 255, 255, 0.95);
        font-size: 0.9rem;
        font-weight: 500;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        text-align: center;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .sidebar-nav {
        padding: 1rem 0;
        position: relative;
        z-index: 2;
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
    }

    .sidebar-nav ul {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .sidebar-nav li {
        margin-bottom: 0.5rem;
    }

    .sidebar-nav a {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        border-right: 4px solid transparent;
        position: relative;
        white-space: nowrap;
        min-height: 60px;
    }

    .sidebar-nav a::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.3s ease;
    }

    .sidebar-nav a:hover::before,
    .sidebar-nav a.active::before {
        transform: scaleX(1);
    }

    .sidebar-nav a:hover,
    .sidebar-nav a.active {
        color: white;
        border-right-color: var(--accent-color);
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(-3px);
    }

    .sidebar-nav a.active {
        background: rgba(245, 158, 11, 0.2);
        border-right-color: var(--accent-color);
        box-shadow: inset 0 0 10px rgba(245, 158, 11, 0.3);
    }

    .sidebar-nav a i {
        margin-left: 15px;
        width: 20px;
        text-align: center;
        font-size: 1.1rem;
        position: relative;
        z-index: 2;
    }

    .sidebar-nav a span {
        position: relative;
        z-index: 2;
        font-weight: 500;
    }

    /* Main Content */
    .main-content {
        margin-right: 280px;
        padding: 2rem;
        min-height: 100vh;
    }

    .page-header {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: var(--shadow);
        margin-bottom: 2rem;
        border-right: 4px solid var(--primary-color);
    }

    .page-header h1 {
        color: var(--primary-color);
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .page-header p {
        color: #6b7280;
        font-size: 1.1rem;
        margin: 0;
    }

    /* Cards */
    .card {
        background: white;
        border-radius: 15px;
        box-shadow: var(--shadow);
        border: none;
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .card-header {
        background: var(--gradient-primary);
        color: white;
        padding: 1.5rem;
        border: none;
        font-weight: 600;
    }

    .card-body {
        padding: 2rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .sidebar {
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .sidebar.active {
            transform: translateX(0);
        }

        .main-content {
            margin-right: 0;
            padding: 1rem;
        }

        .page-header {
            padding: 1.5rem;
        }

        .page-header h1 {
            font-size: 1.5rem;
        }
    }

    /* تحسين الـ scrollbar للـ sidebar */
    .sidebar::-webkit-scrollbar,
    .sidebar-nav::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar::-webkit-scrollbar-track,
    .sidebar-nav::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
    }

    .sidebar::-webkit-scrollbar-thumb,
    .sidebar-nav::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
        transition: background 0.3s ease;
    }

    .sidebar::-webkit-scrollbar-thumb:hover,
    .sidebar-nav::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }

    /* Firefox */
    .sidebar,
    .sidebar-nav {
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
    }

    /* إخفاء الـ scrollbar الأفقي */
    .table-responsive {
        overflow-x: auto;
        overflow-y: hidden;
    }

    .table-responsive::-webkit-scrollbar {
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 10px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: var(--secondary-color);
    }

    /* تحسينات إضافية للقائمة الجانبية */
    .sidebar-nav li:last-child {
        margin-bottom: 2rem; /* مساحة إضافية في النهاية */
    }

    /* تأثير smooth scroll */
    .sidebar,
    .sidebar-nav {
        scroll-behavior: smooth;
    }

    /* تحسين الأداء */
    .sidebar {
        will-change: scroll-position;
        transform: translateZ(0); /* تفعيل hardware acceleration */
    }

    /* إضافة padding في النهاية للتأكد من ظهور آخر عنصر */
    .sidebar-nav ul::after {
        content: '';
        display: block;
        height: 2rem;
        flex-shrink: 0;
    }

    /* تحسين مظهر تسجيل الخروج */
    .sidebar-nav a[href="logout.php"] {
        margin-top: auto;
        background: rgba(220, 53, 69, 0.1);
        border-right-color: #dc3545 !important;
    }

    .sidebar-nav a[href="logout.php"]:hover {
        background: rgba(220, 53, 69, 0.2);
        color: #fff !important;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة التمرير في القائمة الجانبية
    const sidebar = document.querySelector('.sidebar');
    const sidebarNav = document.querySelector('.sidebar-nav');

    if (sidebar && sidebarNav) {
        // إضافة smooth scrolling
        sidebar.style.scrollBehavior = 'smooth';
        sidebarNav.style.scrollBehavior = 'smooth';

        // التأكد من أن القائمة قابلة للتمرير
        function checkScrollable() {
            const navHeight = sidebarNav.scrollHeight;
            const containerHeight = sidebarNav.clientHeight;

            if (navHeight > containerHeight) {
                sidebarNav.style.overflowY = 'auto';
            } else {
                sidebarNav.style.overflowY = 'visible';
            }
        }

        // فحص عند تحميل الصفحة وعند تغيير حجم النافذة
        checkScrollable();
        window.addEventListener('resize', checkScrollable);

        // إضافة تأثير عند التمرير
        sidebarNav.addEventListener('scroll', function() {
            const scrollTop = this.scrollTop;
            const scrollHeight = this.scrollHeight;
            const clientHeight = this.clientHeight;

            // إضافة shadow عند التمرير
            if (scrollTop > 0) {
                this.style.boxShadow = 'inset 0 10px 10px -10px rgba(0,0,0,0.3)';
            } else {
                this.style.boxShadow = 'none';
            }

            // إضافة shadow في الأسفل إذا لم نصل للنهاية
            if (scrollTop + clientHeight < scrollHeight - 10) {
                this.style.borderBottom = '1px solid rgba(255,255,255,0.1)';
            } else {
                this.style.borderBottom = 'none';
            }
        });
    }
});
</script>
