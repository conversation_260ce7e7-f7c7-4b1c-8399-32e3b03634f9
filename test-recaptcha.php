<?php
define('SECURE_ACCESS', true);
include 'config.php';
include 'includes/recaptcha.php';

// جلب إعدادات reCAPTCHA
$recaptcha_settings = getRecaptchaSettings($pdo);

$message = '';
if ($_POST) {
    if (validateRecaptchaInForm($pdo)) {
        $message = '<div class="alert alert-success">✅ reCAPTCHA يعمل بشكل صحيح!</div>';
    } else {
        $message = '<div class="alert alert-danger">❌ فشل التحقق من reCAPTCHA</div>';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار reCAPTCHA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    
    <?php echo getRecaptchaScript($recaptcha_settings['enabled']); ?>
    
    <style>
        body { font-family: 'Tajawal', sans-serif; }
        .g-recaptcha {
            display: flex;
            justify-content: center;
            margin: 1.5rem 0;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار reCAPTCHA</h3>
                    </div>
                    <div class="card-body">
                        <p><strong>حالة reCAPTCHA:</strong></p>
                        <ul>
                            <li>مفعل: <?php echo $recaptcha_settings['enabled'] ? '✅ نعم' : '❌ لا'; ?></li>
                            <li>Site Key: <?php echo !empty($recaptcha_settings['site_key']) ? '✅ موجود' : '❌ غير موجود'; ?></li>
                            <li>Secret Key: <?php echo !empty($recaptcha_settings['secret_key']) ? '✅ موجود' : '❌ غير موجود'; ?></li>
                        </ul>
                        
                        <?php echo $message; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label class="form-label">اسم الاختبار</label>
                                <input type="text" class="form-control" name="test_name" value="اختبار reCAPTCHA" required>
                            </div>
                            
                            <?php if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])): ?>
                            <div class="mb-3">
                                <div class="g-recaptcha" data-sitekey="<?php echo htmlspecialchars($recaptcha_settings['site_key']); ?>"></div>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-warning">
                                reCAPTCHA غير مفعل أو المفاتيح غير مكتملة
                            </div>
                            <?php endif; ?>
                            
                            <button type="submit" class="btn btn-primary">اختبار reCAPTCHA</button>
                        </form>
                        
                        <hr>
                        <p><a href="admin/login.php">صفحة تسجيل الدخول</a></p>
                        <p><a href="contact.php">صفحة اتصل بنا</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
