<?php
define('SECURE_ACCESS', true);
include 'config.php';
include 'includes/recaptcha.php';

// جلب إعدادات reCAPTCHA
$recaptcha_settings = getRecaptchaSettings($pdo);

echo "<h2>تشخيص مشكلة reCAPTCHA</h2>";

echo "<h3>إعدادات reCAPTCHA من قاعدة البيانات:</h3>";
echo "<pre>";
print_r($recaptcha_settings);
echo "</pre>";

echo "<h3>اختبار دالة getRecaptchaScript:</h3>";
echo "النتيجة: " . htmlspecialchars(getRecaptchaScript($recaptcha_settings['enabled']));

echo "<h3>اختبار شرط العرض:</h3>";
if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])) {
    echo "✅ الشرط صحيح - يجب أن يظهر reCAPTCHA";
} else {
    echo "❌ الشرط خاطئ:";
    echo "<br>- مفعل: " . ($recaptcha_settings['enabled'] ? 'نعم' : 'لا');
    echo "<br>- Site Key موجود: " . (!empty($recaptcha_settings['site_key']) ? 'نعم' : 'لا');
}

echo "<h3>اختبار reCAPTCHA مباشر:</h3>";
if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])) {
    echo getRecaptchaScript($recaptcha_settings['enabled']);
    echo '<div style="text-align: center; margin: 20px 0;">';
    echo '<div class="g-recaptcha" data-sitekey="' . htmlspecialchars($recaptcha_settings['site_key']) . '"></div>';
    echo '</div>';
} else {
    echo "لا يمكن عرض reCAPTCHA - الإعدادات غير مكتملة";
}
?>
