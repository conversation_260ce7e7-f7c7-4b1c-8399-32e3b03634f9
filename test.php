<?php
define('SECURE_ACCESS', true);
include 'config.php';

echo "Test successful! Database connection is working.";

// اختبار الاتصال بقاعدة البيانات
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM articles");
    $result = $stmt->fetch();
    echo "<br>Articles count: " . $result['count'];
} catch(PDOException $e) {
    echo "<br>Database error: " . $e->getMessage();
}

// اختبار إعدادات reCAPTCHA
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'recaptcha%'");
    $settings = $stmt->fetchAll();
    echo "<br><br>reCAPTCHA Settings:";
    foreach($settings as $setting) {
        echo "<br>" . $setting['setting_key'] . ": " . $setting['setting_value'];
    }
} catch(PDOException $e) {
    echo "<br>Settings error: " . $e->getMessage();
}
?>
