<?php
define('SECURE_ACCESS', true);
include 'config.php';
include 'includes/recaptcha.php';

echo "<h2>اختبار الموقع والأمان</h2>";

// اختبار الاتصال بقاعدة البيانات
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM articles");
    $result = $stmt->fetch();
    echo "<p>✅ قاعدة البيانات تعمل - عدد المقالات: " . $result['count'] . "</p>";
} catch(PDOException $e) {
    echo "<p>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// اختبار إعدادات reCAPTCHA
echo "<h3>إعدادات reCAPTCHA:</h3>";
$recaptcha_settings = getRecaptchaSettings($pdo);

echo "<p>مفعل: " . ($recaptcha_settings['enabled'] ? '✅ نعم' : '❌ لا') . "</p>";
echo "<p>Site Key: " . (!empty($recaptcha_settings['site_key']) ? '✅ موجود' : '❌ غير موجود') . "</p>";
echo "<p>Secret Key: " . (!empty($recaptcha_settings['secret_key']) ? '✅ موجود' : '❌ غير موجود') . "</p>";

if ($recaptcha_settings['enabled'] && !empty($recaptcha_settings['site_key'])) {
    echo "<p>✅ reCAPTCHA جاهز للعمل!</p>";
} else {
    echo "<p>⚠️ reCAPTCHA غير مكتمل الإعداد</p>";
}

echo "<hr>";
echo "<p><a href='admin/login.php'>اختبار صفحة تسجيل الدخول</a></p>";
echo "<p><a href='articles.php'>اختبار صفحة المقالات</a></p>";
?>
