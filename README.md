# شركة نقرة للتسويق الإلكتروني

موقع احترافي ونظيف لشركة نقرة للتسويق الإلكتروني مع لوحة تحكم بسيطة وفعالة.

## 🎯 المشروع

هذا مشروع احترافي لشركة نقرة للتسويق الإلكتروني يتضمن:
- موقع إلكتروني عصري ومتجاوب
- صفحة منفصلة للأعمال والمشاريع
- قسم الخطط والأسعار
- لوحة تحكم بسيطة وفعالة
- تصميم نظيف ومنظم

## 📁 هيكل المشروع

```
nakraformarketing/
├── index.php              # الصفحة الرئيسية المحسنة (مع انيميشن محسن)
├── services.php           # صفحة الخدمات المتكاملة (6 خدمات)
├── projects.php           # صفحة الأعمال والمشاريع
├── contact.php            # صفحة التواصل الكاملة (محسنة)
├── articles.php           # صفحة المقالات والنصائح (جديدة)
├── config.php             # إعدادات قاعدة البيانات
├── sitemap.xml            # خريطة الموقع للـ SEO (محدثة)
├── robots.txt             # ملف الروبوتات للـ SEO (جديد)
├── README.md              # دليل المشروع الشامل
├── admin/                 # لوحة التحكم الاحترافية (محسنة)
│   ├── dashboard.php      # لوحة التحكم الرئيسية المحسنة
│   ├── index.php          # إعادة توجيه للوحة التحكم
│   ├── login.php          # تسجيل الدخول الآمن
│   ├── logout.php         # تسجيل الخروج
│   ├── projects.php       # إدارة المشاريع
│   ├── messages.php       # إدارة الرسائل
│   └── settings.php       # صفحة الإعدادات (جديدة)
├── assets/                # الملفات الثابتة
│   └── images/            # مجلد الصور
│       ├── logo2.png      # شعار الشركة الرئيسي + فافيكون
│       ├── logo.png       # شعار الشركة البديل
│       └── [صور المشاريع] # صور المشاريع الحقيقية (10 مشاريع)
└── database/              # قاعدة البيانات
    ├── nakra_database.sql # قاعدة البيانات الاحترافية المحسنة
    └── INSTALL.md         # دليل التثبيت
```

## 🚀 التثبيت السريع

### 1. رفع الملفات
```bash
# ارفع جميع ملفات المشروع إلى public_html
```

### 2. إعداد قاعدة البيانات الشاملة الجديدة
- ادخل إلى phpMyAdmin
- اختر قاعدة البيانات: `meedpsco_hostmeed`
- اذهب إلى تبويب SQL
- انسخ والصق محتوى ملف `database/nakra_database.sql` بالكامل
- اضغط تنفيذ

### 3. تحديث الإعدادات
في ملف `config.php`:
```php
define('DB_NAME', 'meedpsco_hostmeed');
define('DB_USER', 'meedpsco_hostmeed');
define('DB_PASS', 'meedpsco_hostmeed');
```

### 4. اختبار الموقع
- **الموقع الرئيسي**: https://bag.meedps.com/
- **صفحة الخدمات**: https://bag.meedps.com/services.php
- **صفحة الأعمال**: https://bag.meedps.com/projects.php
- **صفحة التواصل**: https://bag.meedps.com/contact.php
- **صفحة المقالات**: https://bag.meedps.com/articles.php
- **لوحة التحكم**: https://bag.meedps.com/admin/dashboard.php

## 🔑 بيانات الدخول

### لوحة التحكم
- **اسم المستخدم**: admin
- **كلمة المرور**: password
- **رابط لوحة التحكم**: https://bag.meedps.com/admin/login.php
