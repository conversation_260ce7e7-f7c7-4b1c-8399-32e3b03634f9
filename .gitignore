# Nakra Digital Marketing Website - Git Ignore File

# Environment and Configuration Files
.env
.env.local
.env.production
config/local.php
config/production.php

# Database
*.sql
*.sqlite
*.db

# Logs
logs/
*.log
error_log
access_log

# Cache
cache/
tmp/
temp/

# Uploads (exclude user uploaded files but keep directory structure)
uploads/*
!uploads/.gitkeep
!uploads/*/
uploads/*/*.jpg
uploads/*/*.jpeg
uploads/*/*.png
uploads/*/*.gif
uploads/*/*.webp
uploads/*/*.pdf
uploads/*/*.doc
uploads/*/*.docx

# Backup files
*.bak
*.backup
*.old
*.orig
*.save
*.swp
*.tmp

# IDE and Editor files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.atom/
.brackets.json

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Composer
vendor/
composer.lock
composer.phar

# Build files
dist/
build/
assets/css/*.min.css
assets/js/*.min.js
assets/js/app.js

# Sass
.sass-cache/
*.css.map
*.sass.map
*.scss.map

# PHP
*.php~
.phpunit.result.cache

# Security
.htpasswd
.htaccess.backup
wp-config.php

# Temporary files
*.tmp
*.temp
*~

# Archive files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Documentation build
docs/_build/

# Coverage reports
coverage/
*.lcov

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Local development
local/
dev/

# Testing
tests/coverage/
phpunit.xml
.phpunit.result.cache

# Deployment
deploy/
deployment/

# Custom
custom/
private/
